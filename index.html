<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ankara Trafik Kazaları</title>
    <link
      href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css"
      rel="stylesheet"
    />
    <style>
      html,
      body {
        height: 100%;
        margin: 0;
        padding: 0;
        width: 100%;
        font-family: Arial, sans-serif;
        background: #181a1b;
        color: #fff;
      }
      #map {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100vw;
        height: 100vh;
        z-index: 1;
      }
      #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(24, 26, 27, 0.95);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        transition: opacity 0.3s ease;
      }
      #loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #333;
        border-top: 4px solid #fdae61;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      #loading-text {
        font-size: 1.2em;
        color: #fff;
        margin-bottom: 10px;
      }
      #loading-progress {
        width: 300px;
        height: 6px;
        background: #333;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 10px;
      }
      #loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #2b83ba, #fdae61, #d7191c);
        width: 0%;
        transition: width 0.3s ease;
      }
      #loading-status {
        font-size: 0.9em;
        color: #ccc;
        text-align: center;
        max-width: 400px;
      }
      #controls {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(24, 26, 27, 0.95);
        padding: 16px 20px;
        border-radius: 8px;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        min-width: 220px;
        transition: transform 0.3s, opacity 0.3s, left 0.3s, width 0.3s;
      }
      #controls.minimized {
        min-width: 0;
        width: 48px;
        height: 48px;
        padding: 0;
        left: 12px;
        top: 12px;
        opacity: 0.85;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      #controls .minimize-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        background: none;
        border: none;
        color: #fff;
        font-size: 1.3em;
        cursor: pointer;
        z-index: 20;
        padding: 0 4px;
        border-radius: 4px;
        transition: background 0.2s;
      }
      #controls .minimize-btn:active,
      #controls .minimize-btn:focus {
        background: #232526;
        outline: none;
      }
      #controls.minimized .minimize-btn {
        right: 8px;
        left: 8px;
        top: 8px;
        font-size: 1.6em;
        background: none;
        color: #fdae61;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
      #controls.minimized *:not(.minimize-btn) {
        display: none !important;
      }
      #legend {
        margin-top: 16px;
      }
      .legend-row {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
      }
      .legend-color {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        border-radius: 3px;
        display: inline-block;
      }
      h1 {
        font-size: 1.2em;
        margin: 0 0 10px 0;
      }
      #info-box {
        position: fixed;
        top: 24px;
        right: 24px;
        background: rgba(30, 32, 34, 0.98);
        color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
        min-width: 260px;
        max-width: 340px;
        z-index: 100;
        padding: 18px 22px 18px 18px;
        font-size: 1em;
        transition: opacity 0.2s;
      }
      #close-info {
        position: absolute;
        top: 8px;
        right: 10px;
        background: none;
        border: none;
        color: #fff;
        font-size: 1.3em;
        cursor: pointer;
        padding: 0;
      }
      #info-content {
        margin-top: 8px;
        word-break: break-word;
      }
      #dashboard-stats {
        position: fixed;
        bottom: 24px;
        right: 24px;
        background: rgba(30, 32, 34, 0.98);
        color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        z-index: 100;
        padding: 20px 24px;
        text-align: center;
        min-width: 140px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
      #total-accidents {
        font-size: 2.5em;
        font-weight: bold;
        color: #fdae61;
        margin: 0;
        line-height: 1;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
      #stats-label {
        font-size: 0.9em;
        color: #ccc;
        margin: 4px 0 0 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      #stats-subtitle {
        font-size: 0.75em;
        color: #888;
        margin: 8px 0 0 0;
        font-style: italic;
      }
      #filter-panel {
        margin-top: 18px;
      }
      #toggle-filters {
        background: #232526;
        color: #fff;
        border: none;
        border-radius: 5px;
        padding: 6px 14px;
        font-size: 1em;
        cursor: pointer;
        margin-bottom: 8px;
        width: 100%;
        text-align: left;
      }
      #filters {
        margin-top: 8px;
        padding: 8px 0 0 0;
        border-top: 1px solid #333;
      }
      #filters label {
        display: block;
        margin-bottom: 10px;
        font-size: 0.98em;
      }
      #hour-slider-container {
        margin: 12px 0 18px 0;
        width: 100%;
      }
      .range-slider {
        position: relative;
        width: 100%;
        height: 32px;
        margin: 0 auto;
      }
      .range-slider input[type="range"] {
        position: absolute;
        pointer-events: none;
        -webkit-appearance: none;
        width: 100%;
        height: 8px;
        background: transparent;
        margin: 0;
        top: 12px;
      }
      .range-slider input[type="range"]::-webkit-slider-thumb {
        pointer-events: all;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #ffffff;
        border: 2px solid #222;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        -webkit-appearance: none;
        cursor: pointer;
        margin-top: -5px;
      }
      .range-slider input[type="range"]::-moz-range-thumb {
        pointer-events: all;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #ffffff;
        border: 2px solid #222;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        cursor: pointer;
      }
      .range-slider input[type="range"]::-ms-thumb {
        pointer-events: all;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #ffffff;
        border: 2px solid #222;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        cursor: pointer;
      }
      .range-slider input[type="range"]:focus {
        outline: none;
      }
      .range-slider .slider-track {
        position: absolute;
        height: 8px;
        border-radius: 4px;
        background: #444;
        width: 100%;
        top: 12px;
        z-index: 0;
      }
      .range-slider .slider-range {
        position: absolute;
        height: 8px;
        border-radius: 4px;
        background: linear-gradient(90deg, #d7191c, #fdae61, #2b83ba);
        z-index: 0;
        top: 12px;
      }
      #hour-range-label {
        display: block;
        margin-bottom: 4px;
        font-weight: bold;
        font-size: 1.05em;
        text-align: left;
      }
      #filters input[type="date"] {
        background: #232526;
        color: #fff;
        border: 1px solid #444;
        border-radius: 3px;
        padding: 2px 6px;
        margin: 0 2px;
      }
      @media (max-width: 600px) {
        #controls {
          min-width: 0;
          width: 90vw;
          max-width: 98vw;
          padding: 4px 2vw 4px 2vw;
          left: 2vw;
          top: 8px;
          font-size: 0.92em;
          border-radius: 6px;
        }
        #controls h1 {
          font-size: 1em;
          margin-bottom: 6px;
        }
        #controls .minimize-btn {
          font-size: 1.4em;
          top: 4px;
          right: 4px;
        }
        #filter-panel {
          font-size: 0.92em;
          margin-top: 8px;
        }
        #legend {
          font-size: 0.92em;
          margin-top: 8px;
        }
        #dashboard-stats {
          bottom: 40px;
          right: 8px;
          padding: 10px 8px;
          min-width: 90px;
        }
        #total-accidents {
          font-size: 1.3em;
        }
        #stats-label {
          font-size: 0.7em;
        }
        #controls.minimized {
          min-width: 0;
          width: 40px;
          height: 40px;
          left: 4px;
          top: 4px;
        }
        #controls label,
        #controls input,
        #controls select,
        #controls button {
          font-size: 0.95em;
        }
      }
      .mapboxgl-popup.hexbin-popup-dark .mapboxgl-popup-content {
        background: #232526;
        color: #fff;
        border-radius: 6px;
        font-size: 1.1em;
      }
      .mapboxgl-ctrl-bottom-left {
        display: none;
      }
      .mapbox-improve-map {
        display: none;
      }
      #brand-copyright {
        position: fixed;
        left: 24px;
        bottom: 24px;
        background: rgba(30, 32, 34, 0.25);
        color: #fdae61;
        border-radius: 6px;
        z-index: 101;
        padding: 2px 8px;
        font-size: 0.85em;
        min-width: 0;
        text-align: left;
        font-family: inherit;
        transition: opacity 0.2s;
      }
      #brand-copyright a {
        color: #fdae61;
        text-decoration: none;
        font-weight: bold;
        opacity: 0.7;
        transition: opacity 0.2s;
      }
      #brand-copyright a:hover {
        text-decoration: underline;
        opacity: 1;
      }
      @media (max-width: 600px) {
        #brand-copyright {
          left: 6px;
          bottom: 6px;
          padding: 2px 6px;
          font-size: 0.8em;
          min-width: 0;
        }
      }
    </style>
  </head>
  <body>
    <div id="loading-overlay">
      <div id="loading-spinner"></div>
      <div id="loading-text">Loading Crash Data...</div>
      <div id="loading-progress">
        <div id="loading-progress-bar"></div>
      </div>
      <div id="loading-status">Connecting to Chicago Data Portal...</div>
    </div>
    <div id="map"></div>
    <div id="controls">
      <button
        class="minimize-btn"
        id="minimize-controls"
        title="Minimize controls"
        aria-label="Minimize controls"
      >
        –
      </button>
      <button
        class="minimize-btn"
        id="restore-controls"
        title="Show controls"
        aria-label="Show controls"
        style="display: none"
      >
        ☰
      </button>
      <h1>Kentsel Trafik Kazası - Ankara</h1>
      <div id="legend"></div>
      <div id="filter-panel">
        <h1>Date and Time</h1>
        <div id="filters">
          <div id="hour-slider-container">
            <span id="hour-range-label">0:00 – 23:00</span>
            <div class="range-slider">
              <div class="slider-track"></div>
              <div class="slider-range" id="slider-range"></div>
              <input
                type="range"
                id="hour-start"
                min="0"
                max="23"
                value="0"
                step="1"
              />
              <input
                type="range"
                id="hour-end"
                min="0"
                max="23"
                value="23"
                step="1"
              />
            </div>
          </div>
          <label>
            Date Range:
            <input type="date" id="date-start" />
            <input type="date" id="date-end" />
          </label>
          <div id="crash-type-filter-container">
            <label><b>Crash Type:</b></label>
            <div id="crash-type-checkboxes"></div>
            <div
              id="selected-crash-types"
              style="margin-top: 6px; font-size: 0.95em; color: #fdae61"
            ></div>
          </div>
        </div>
      </div>
      <!-- Future: Filters, charts, etc. -->
    </div>
    <div id="info-box" style="display: none">
      <button id="close-info">×</button>
      <div id="info-content"></div>
    </div>
    <div id="dashboard-stats">
      <div id="total-accidents">0</div>
      <div id="stats-label">Total Accidents</div>
      <div id="stats-subtitle">All Accidents</div>
    </div>

    <script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@turf/turf@6.5.0/turf.min.js"></script>
    <script type="module">
      import {
        loadGeoJSON,
        createHexbins,
        getHexbinColor,
        getHexbinHeight,
        buildLegend,
      } from "./webgis.js";

      mapboxgl.accessToken =
        "pk.eyJ1IjoibW9oYW1tYWR2aGIiLCJhIjoiY20ydDA3NGEyMDJ4bzJvcGRtM2FvM2F2eiJ9.LbmbosKTshtEeM0udIeUJQ"; // Demo token, replace for production

      const map = new mapboxgl.Map({
        container: "map",
        projection: "globe",
        style: "mapbox://styles/mapbox/dark-v10",
        center: [-87.6793, 41.8281],
        zoom: 10.53,
        bearing: 37.6,
        pitch: 53,
      });

      let hexbinGeojson;
      let crashData;
      let filteredCrashData;
      let pointsLayerAdded = false;
      let hourStart = 0,
        hourEnd = 23;
      let dateStart = null,
        dateEnd = null;
      let crashTypeOptions = [];
      let selectedCrashTypes = [];

      // Loading state management
      function updateLoadingProgress(progress, status) {
        const progressBar = document.getElementById("loading-progress-bar");
        const statusText = document.getElementById("loading-status");
        progressBar.style.width = `${progress}%`;
        if (status) statusText.textContent = status;
      }

      function hideLoadingOverlay() {
        const overlay = document.getElementById("loading-overlay");
        overlay.style.opacity = "0";
        setTimeout(() => {
          overlay.style.display = "none";
        }, 300);
      }

      // Enhanced data loading with caching and error handling
      async function loadCrashDataWithProgress() {
        const CACHE_KEY = "chicago_crash_data_cache";
        const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

        try {
          updateLoadingProgress(10, "Checking for cached data...");

          // Check for cached data
          const cached = localStorage.getItem(CACHE_KEY);
          if (cached) {
            const { data, timestamp } = JSON.parse(cached);
            const age = Date.now() - timestamp;

            if (age < CACHE_DURATION) {
              updateLoadingProgress(100, "Loading from cache...");
              await new Promise((resolve) => setTimeout(resolve, 500)); // Brief delay for UX
              return data;
            }
          }

          updateLoadingProgress(
            20,
            "Fetching data from Chicago Data Portal..."
          );

          // Fetch fresh data with timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

          const response = await fetch(
            "https://data.cityofchicago.org/resource/85ca-t3if.geojson",
            {
              signal: controller.signal,
              headers: {
                Accept: "application/json",
                "User-Agent": "WebGIS-Crash-Analysis/1.0",
              },
            }
          );

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          updateLoadingProgress(60, "Processing data...");

          const data = await response.json();

          updateLoadingProgress(80, "Caching data for faster future loads...");

          // Cache the data
          localStorage.setItem(
            CACHE_KEY,
            JSON.stringify({
              data: data,
              timestamp: Date.now(),
            })
          );

          updateLoadingProgress(100, "Data loaded successfully!");
          await new Promise((resolve) => setTimeout(resolve, 500)); // Brief delay for UX

          return data;
        } catch (error) {
          console.error("Error loading crash data:", error);

          // Try to load from cache even if expired
          const cached = localStorage.getItem(CACHE_KEY);
          if (cached) {
            updateLoadingProgress(
              90,
              "Loading from expired cache due to network error..."
            );
            const { data } = JSON.parse(cached);
            await new Promise((resolve) => setTimeout(resolve, 500));
            return data;
          }

          // Show error message
          document.getElementById("loading-text").textContent =
            "Error Loading Data";
          document.getElementById("loading-status").innerHTML = `
          Failed to load data: ${error.message}<br>
          <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #fdae61; color: #000; border: none; border-radius: 4px; cursor: pointer;">
            Retry
          </button>
        `;
          throw error;
        }
      }

      async function main() {
        try {
          updateLoadingProgress(0, "Initializing application...");

          crashData = await loadCrashDataWithProgress();

          updateLoadingProgress(85, "Processing crash data...");

          // Filter out invalid features and augment with crash_hour and crash_day
          const validFeatures = crashData.features.filter((f) => {
            // Check if feature has valid geometry and coordinates
            if (
              !f.geometry ||
              f.geometry.type !== "Point" ||
              !f.geometry.coordinates
            ) {
              return false;
            }

            const [lng, lat] = f.geometry.coordinates;
            if (
              typeof lng !== "number" ||
              typeof lat !== "number" ||
              isNaN(lng) ||
              isNaN(lat) ||
              lng < -180 ||
              lng > 180 ||
              lat < -90 ||
              lat > 90
            ) {
              return false;
            }

            // Check if crash_date is valid
            if (!f.properties.crash_date) {
              return false;
            }

            return true;
          });

          console.log(
            `Filtered to ${validFeatures.length} valid features out of ${crashData.features.length} total`
          );

          // Replace features with valid ones
          crashData.features = validFeatures;

          // Augment features with crash_hour and crash_day
          crashData.features.forEach((f) => {
            try {
              const d = new Date(f.properties.crash_date);
              if (isNaN(d.getTime())) {
                console.warn("Invalid date:", f.properties.crash_date);
                f.properties.crash_hour = 0;
                f.properties.crash_day = "2020-01-01"; // fallback date
              } else {
                f.properties.crash_hour = d.getHours();
                f.properties.crash_day = d.toISOString().slice(0, 10);
              }
            } catch (dateError) {
              console.warn(
                "Error processing date:",
                f.properties.crash_date,
                dateError
              );
              f.properties.crash_hour = 0;
              f.properties.crash_day = "2020-01-01"; // fallback date
            }
          });

          updateLoadingProgress(90, "Setting up filters...");

          // Extract unique crash_type values
          crashTypeOptions = Array.from(
            new Set(crashData.features.map((f) => f.properties.crash_type))
          ).sort();
          selectedCrashTypes = [...crashTypeOptions]; // Default: all selected
          renderCrashTypeCheckboxes();

          // Set default date range
          const allDates = crashData.features
            .map((f) => f.properties.crash_day)
            .sort();
          dateStart = allDates[0];
          dateEnd = allDates[allDates.length - 1];
          const dateStartInput = document.getElementById("date-start");
          const dateEndInput = document.getElementById("date-end");
          dateStartInput.value = dateStart;
          dateEndInput.value = dateEnd;
          dateStartInput.min = dateStart;
          dateStartInput.max = dateEnd;
          dateEndInput.min = dateStart;
          dateEndInput.max = dateEnd;

          updateLoadingProgress(95, "Creating visualization...");

          filteredCrashData = filterCrashes(
            crashData,
            hourStart,
            hourEnd,
            dateStart,
            dateEnd,
            selectedCrashTypes
          );
          hexbinGeojson = createHexbins(filteredCrashData, 0.5); // 0.5km hex radius
          addHexbinLayer(hexbinGeojson);
          buildLegend(hexbinGeojson, "legend");
          map.on("zoom", handleZoomLayers);
          setupFilterUI(dateStart, dateEnd);
          updateDashboardStats();

          updateLoadingProgress(100, "Ready!");
          setTimeout(hideLoadingOverlay, 1000);
        } catch (error) {
          console.error("Application initialization failed:", error);
          document.getElementById("loading-text").textContent =
            "Application Error";
          document.getElementById("loading-status").innerHTML = `
          Failed to initialize: ${error.message}<br>
          <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #fdae61; color: #000; border: none; border-radius: 4px; cursor: pointer;">
            Reload Page
          </button>
        `;
        }
      }

      function filterCrashes(
        data,
        hourStart,
        hourEnd,
        dateStart,
        dateEnd,
        crashTypes
      ) {
        // Fallback to min/max if dateStart/dateEnd are empty
        const allDates = data.features
          .map((f) => f.properties.crash_day)
          .sort();
        const minDate = allDates[0];
        const maxDate = allDates[allDates.length - 1];
        const start =
          dateStart && dateStart.length === 10 ? dateStart : minDate;
        const end = dateEnd && dateEnd.length === 10 ? dateEnd : maxDate;
        return {
          ...data,
          features: data.features.filter((f) => {
            const h = f.properties.crash_hour;
            const d = f.properties.crash_day;
            const ct = f.properties.crash_type;
            return (
              h >= hourStart &&
              h <= hourEnd &&
              d >= start &&
              d <= end &&
              (!crashTypes ||
                crashTypes.length === 0 ||
                crashTypes.includes(ct))
            );
          }),
        };
      }

      function updateMapWithFilters() {
        filteredCrashData = filterCrashes(
          crashData,
          hourStart,
          hourEnd,
          dateStart,
          dateEnd,
          selectedCrashTypes
        );
        hexbinGeojson = createHexbins(filteredCrashData, 0.5);
        if (map.getLayer("hexbins")) map.removeLayer("hexbins");
        if (map.getSource("hexbins")) map.removeSource("hexbins");
        addHexbinLayer(hexbinGeojson);
        buildLegend(hexbinGeojson, "legend");
        if (pointsLayerAdded) {
          if (map.getSource("crash-points"))
            map.getSource("crash-points").setData(filteredCrashData);
        }
        updateDashboardStats();
      }

      function renderCrashTypeCheckboxes() {
        const container = document.getElementById("crash-type-checkboxes");
        container.innerHTML = "";
        crashTypeOptions.forEach((type) => {
          const id = "crash-type-" + type.replace(/[^a-zA-Z0-9]/g, "_");
          const label = document.createElement("label");
          label.style.display = "block";
          label.style.marginBottom = "2px";
          const checkbox = document.createElement("input");
          checkbox.type = "checkbox";
          checkbox.value = type;
          checkbox.id = id;
          checkbox.checked = selectedCrashTypes.includes(type);
          checkbox.addEventListener("change", () => {
            if (checkbox.checked) {
              if (!selectedCrashTypes.includes(type))
                selectedCrashTypes.push(type);
            } else {
              // Prevent deselecting the last option
              if (selectedCrashTypes.length > 1) {
                selectedCrashTypes = selectedCrashTypes.filter(
                  (t) => t !== type
                );
              } else {
                // Re-check the checkbox if it's the last selected option
                checkbox.checked = true;
                return;
              }
            }
            updateSelectedCrashTypesDisplay();
            updateMapWithFilters();
          });
          label.appendChild(checkbox);
          label.appendChild(document.createTextNode(" " + type));
          container.appendChild(label);
        });
        updateSelectedCrashTypesDisplay();
      }

      function updateSelectedCrashTypesDisplay() {
        const display = document.getElementById("selected-crash-types");
        if (!display) return;
        if (
          selectedCrashTypes.length === 0 ||
          selectedCrashTypes.length === crashTypeOptions.length
        ) {
          display.textContent = "All crash types selected";
        } else {
          display.textContent = "Selected: " + selectedCrashTypes.join(", ");
        }
      }

      function setupFilterUI(defaultStart, defaultEnd) {
        const hourStartInput = document.getElementById("hour-start");
        const hourEndInput = document.getElementById("hour-end");
        const hourLabel = document.getElementById("hour-range-label");
        const sliderRange = document.getElementById("slider-range");
        const dateStartInput = document.getElementById("date-start");
        const dateEndInput = document.getElementById("date-end");
        const minHour = 0,
          maxHour = 23;
        function formatHour(h) {
          return `${h}:00`;
        }
        function updateHourLabel() {
          hourLabel.textContent = `${formatHour(hourStart)} – ${formatHour(
            hourEnd
          )}`;
        }
        function updateSliderRange() {
          const percent1 = ((hourStart - minHour) / (maxHour - minHour)) * 100;
          const percent2 = ((hourEnd - minHour) / (maxHour - minHour)) * 100;
          sliderRange.style.left = percent1 + "%";
          sliderRange.style.width = percent2 - percent1 + "%";
        }
        function syncHourInputs() {
          if (hourStart > hourEnd) {
            [hourStart, hourEnd] = [hourEnd, hourStart];
          }
          hourStartInput.value = hourStart;
          hourEndInput.value = hourEnd;
          updateHourLabel();
          updateSliderRange();
          updateMapWithFilters();
        }
        hourStartInput.addEventListener("input", () => {
          hourStart = Math.min(
            Number(hourStartInput.value),
            Number(hourEndInput.value)
          );
          hourEnd = Math.max(
            Number(hourStartInput.value),
            Number(hourEndInput.value)
          );
          syncHourInputs();
        });
        hourEndInput.addEventListener("input", () => {
          hourStart = Math.min(
            Number(hourStartInput.value),
            Number(hourEndInput.value)
          );
          hourEnd = Math.max(
            Number(hourStartInput.value),
            Number(hourEndInput.value)
          );
          syncHourInputs();
        });
        // Initial draw
        updateHourLabel();
        updateSliderRange();
        // Date input listeners
        dateStartInput.addEventListener("change", () => {
          dateStart =
            dateStartInput.value && dateStartInput.value.length === 10
              ? dateStartInput.value
              : defaultStart;
          updateMapWithFilters();
        });
        dateEndInput.addEventListener("change", () => {
          dateEnd =
            dateEndInput.value && dateEndInput.value.length === 10
              ? dateEndInput.value
              : defaultEnd;
          updateMapWithFilters();
        });
      }

      function addHexbinLayer(hexbinGeojson) {
        if (map.getSource("hexbins")) map.removeLayer("hexbins");
        if (map.getLayer("hexbins")) map.removeLayer("hexbins");
        if (map.getSource("hexbins")) map.removeSource("hexbins");
        map.addSource("hexbins", {
          type: "geojson",
          data: hexbinGeojson,
        });
        map.addLayer({
          id: "hexbins",
          type: "fill-extrusion",
          source: "hexbins",
          paint: {
            "fill-extrusion-color": [
              "case",
              ["has", "count"],
              [
                "interpolate",
                ["linear"],
                ["get", "count"],
                ...getHexbinColor(hexbinGeojson),
              ],
              "#888",
            ],
            "fill-extrusion-height": [
              "interpolate",
              ["linear"],
              ["get", "count"],
              ...getHexbinHeight(hexbinGeojson),
            ],
            "fill-extrusion-opacity": 0.85,
          },
        });
        // --- Hexbin hover popup logic ---
        let hexbinPopup;
        map.on("mousemove", "hexbins", (e) => {
          const feature = e.features && e.features[0];
          if (!feature) return;
          const count = feature.properties.count;
          if (!hexbinPopup) {
            hexbinPopup = new mapboxgl.Popup({
              closeButton: false,
              closeOnClick: false,
              className: "hexbin-popup-dark",
            });
          }
          hexbinPopup
            .setLngLat(e.lngLat)
            .setHTML(
              `<div style=\"color:#fff; border-radius:6px; font-size:1.1em; ">` +
                `<b>${count}</b> accident${count !== 1 ? "s" : ""}` +
                `</div>`
            )
            .addTo(map);
        });
        map.on("mouseleave", "hexbins", () => {
          if (hexbinPopup) hexbinPopup.remove();
        });
      }

      function addPointsLayer() {
        if (pointsLayerAdded) return;
        map.addSource("crash-points", {
          type: "geojson",
          data: filteredCrashData,
        });
        map.addLayer({
          id: "crash-points",
          type: "circle",
          source: "crash-points",
          paint: {
            "circle-radius": 5,
            "circle-color": "#fffb00",
            "circle-stroke-width": 1,
            "circle-stroke-color": "#222",
            "circle-opacity": 0.85,
          },
        });
        pointsLayerAdded = true;

        // Add click event for points
        map.on("click", "crash-points", (e) => {
          const feature = e.features && e.features[0];
          if (feature) showInfoBox(feature.properties);
        });

        // Change cursor on hover
        map.on("mouseenter", "crash-points", () => {
          map.getCanvas().style.cursor = "pointer";
        });
        map.on("mouseleave", "crash-points", () => {
          map.getCanvas().style.cursor = "";
        });
      }

      function handleZoomLayers() {
        const zoom = map.getZoom();
        if (zoom >= 14) {
          addPointsLayer();
          if (map.getLayer("hexbins"))
            map.setLayoutProperty("hexbins", "visibility", "none");
          if (map.getLayer("crash-points"))
            map.setLayoutProperty("crash-points", "visibility", "visible");
        } else {
          if (map.getLayer("hexbins"))
            map.setLayoutProperty("hexbins", "visibility", "visible");
          if (map.getLayer("crash-points"))
            map.setLayoutProperty("crash-points", "visibility", "none");
        }
        // Always update points data if present
        if (pointsLayerAdded && map.getSource("crash-points")) {
          map.getSource("crash-points").setData(filteredCrashData);
        }
      }

      // Info box logic
      function showInfoBox(props) {
        const infoBox = document.getElementById("info-box");
        const infoContent = document.getElementById("info-content");
        infoBox.style.display = "block";
        infoContent.innerHTML = `
        <table style="width:100%; border-collapse:separate; border-spacing:0 6px; direction:ltr; font-size:0.8em;">
          <tr style="padding:5px"><td><b>Location Address</b></td><td>${
            props.street_no || ""
          } ${props.street_direction || ""} ${props.street_name || ""}</td></tr>
          <tr style="padding:5px"><td><b>Date & Time</b></td><td>${
            props.crash_date || ""
          }</td></tr>
          <tr style="padding:5px"><td><b>Crash Type (Primary & General)</b></td><td>${
            props.first_crash_type || ""
          }${props.crash_type ? ", " + props.crash_type : ""}</td></tr>
          <tr style="padding:5px"><td><b>Traffic Control, Road Status, Type</b></td><td>${
            props.traffic_control_device || ""
          }${props.device_condition ? ", " + props.device_condition : ""}${
          props.trafficway_type ? ", " + props.trafficway_type : ""
        }${props.alignment ? ", " + props.alignment : ""}${
          props.roadway_surface_cond ? ", " + props.roadway_surface_cond : ""
        }</td></tr>
          <tr style="padding:5px"><td><b>Weather & Lighting</b></td><td>${
            props.weather_condition || ""
          }${
          props.lighting_condition ? ", " + props.lighting_condition : ""
        }</td></tr>
          <tr style="padding:5px"><td><b>Report Type & Police Notified</b></td><td>${
            props.report_type || ""
          }${
          props.date_police_notified ? ", " + props.date_police_notified : ""
        }</td></tr>
          <tr style="padding:5px"><td><b>Injuries & Severity</b></td><td>${
            props.injuries_total || ""
          }${props.most_severe_injury ? ", " + props.most_severe_injury : ""}${
          props.injuries_fatal ? ", Fatal: " + props.injuries_fatal : ""
        }${
          props.injuries_incapacitating
            ? ", Severe: " + props.injuries_incapacitating
            : ""
        }</td></tr>
          <tr style="padding:5px"><td><b>Primary & Secondary Cause</b></td><td>${
            props.prim_contributory_cause || ""
          }${
          props.sec_contributory_cause
            ? ", " + props.sec_contributory_cause
            : ""
        }</td></tr>
          <tr style="padding:5px"><td><b>Speed Limit, Units, Damage</b></td><td>${
            props.posted_speed_limit || ""
          }${props.num_units ? ", " + props.num_units + " units" : ""}${
          props.damage ? ", Damage: " + props.damage : ""
        }</td></tr>
        </table>
      `;
      }
      document.getElementById("close-info").onclick = function () {
        document.getElementById("info-box").style.display = "none";
      };

      function updateDashboardStats() {
        const totalAccidents = filteredCrashData.features.length;
        const allAccidents = crashData.features.length;
        const totalAccidentsElement =
          document.getElementById("total-accidents");
        const statsSubtitle = document.getElementById("stats-subtitle");

        totalAccidentsElement.textContent = totalAccidents;

        // Update subtitle based on whether filters are applied
        if (totalAccidents === allAccidents) {
          statsSubtitle.textContent = "All Accidents";
        } else {
          statsSubtitle.textContent = "Filtered Results";
        }
      }

      map.on("load", main);

      // Minimize/restore controls logic
      const controls = document.getElementById("controls");
      const minimizeBtn = document.getElementById("minimize-controls");
      const restoreBtn = document.getElementById("restore-controls");
      minimizeBtn.onclick = function (e) {
        e.stopPropagation();
        controls.classList.add("minimized");
        minimizeBtn.style.display = "none";
        restoreBtn.style.display = "block";
      };
      restoreBtn.onclick = function (e) {
        e.stopPropagation();
        controls.classList.remove("minimized");
        minimizeBtn.style.display = "block";
        restoreBtn.style.display = "none";
      };
      // Allow tap/click on minimized controls to restore
      controls.addEventListener("click", function (e) {
        if (controls.classList.contains("minimized") && e.target === controls) {
          controls.classList.remove("minimized");
          minimizeBtn.style.display = "block";
          restoreBtn.style.display = "none";
        }
      });

      // Auto-minimize controls on mobile
      if (window.innerWidth <= 600) {
        window.addEventListener("DOMContentLoaded", () => {
          const controls = document.getElementById("controls");
          const minimizeBtn = document.getElementById("minimize-controls");
          if (controls && !controls.classList.contains("minimized")) {
            controls.classList.add("minimized");
            minimizeBtn.style.display = "none";
            const restoreBtn = document.getElementById("restore-controls");
            if (restoreBtn) restoreBtn.style.display = "block";
          }
        });
      }
    </script>
    <script type="module" src="webgis.js"></script>
  </body>
</html>
