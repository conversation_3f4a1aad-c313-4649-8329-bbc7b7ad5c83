// webgis.js

// Load GeoJSON data from a local file with enhanced error handling
export async function loadGeoJSON(url) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        Accept: "application/json",
        "User-Agent": "WebGIS-Crash-Analysis/1.0",
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error loading GeoJSON:", error);
    throw new Error(`Failed to load data from ${url}: ${error.message}`);
  }
}

// Validate if a feature has valid coordinates
function isValidPoint(feature) {
  if (!feature || !feature.geometry || feature.geometry.type !== "Point") {
    return false;
  }

  const coords = feature.geometry.coordinates;
  if (!Array.isArray(coords) || coords.length < 2) {
    return false;
  }

  const [lng, lat] = coords;
  return (
    typeof lng === "number" &&
    typeof lat === "number" &&
    !isNaN(lng) &&
    !isNaN(lat) &&
    lng >= -180 &&
    lng <= 180 &&
    lat >= -90 &&
    lat <= 90
  );
}

// Optimized 3D hexbin clusters from point data using turf.js
export function createHexbins(geojson, radiusKm = 2) {
  if (!geojson || !geojson.features || geojson.features.length === 0) {
    console.warn("No features found in GeoJSON data");
    return {
      type: "FeatureCollection",
      features: [],
    };
  }

  try {
    // Filter out invalid points first
    const validFeatures = geojson.features.filter(isValidPoint);

    if (validFeatures.length === 0) {
      console.warn("No valid point features found in GeoJSON data");
      return {
        type: "FeatureCollection",
        features: [],
      };
    }

    console.log(
      `Processing ${validFeatures.length} valid points out of ${geojson.features.length} total features`
    );

    // Create a clean GeoJSON with only valid points
    const cleanGeoJSON = {
      type: "FeatureCollection",
      features: validFeatures,
    };
    console.log(
      "Cleaned GeoJSON created with only valid points:",
      cleanGeoJSON
    );

    // Get bbox and create hex grid
    const bbox = turf.bbox(cleanGeoJSON);
    console.log("bbox created:", bbox);
    const hexGrid = turf.hexGrid(bbox, radiusKm, { units: "kilometers" });
    console.log("hexGrid created:", hexGrid);

    // Use a more efficient approach for counting points
    const hexbins = [];

    hexGrid.features.forEach((hex) => {
      // Count points within this hex using a more efficient method
      let count = 0;
      const hexPolygon = hex.geometry;

      for (const point of validFeatures) {
        try {
          if (turf.booleanPointInPolygon(point, hexPolygon)) {
            console.log("Point in polygon:", point);
            count++;
          }
        } catch (pointError) {
          console.warn(
            "Error checking point in polygon:",
            pointError,
            "Point:",
            point
          );
          continue;
        }
      }

      if (count > 0) {
        hex.properties.count = count;
        hexbins.push(hex);
      }
    });

    console.log(`Created ${hexbins.length} hexbins with data`);
    return {
      type: "FeatureCollection",
      features: hexbins,
    };
  } catch (error) {
    console.error("Error creating hexbins:", error);
    return {
      type: "FeatureCollection",
      features: [],
    };
  }
}

// Color scale for hexbins based on count with improved color distribution
export function getHexbinColor(hexbinGeojson) {
  if (
    !hexbinGeojson ||
    !hexbinGeojson.features ||
    hexbinGeojson.features.length === 0
  ) {
    return [0, "#888", 1, "#888"];
  }

  try {
    // Find min/max
    const counts = hexbinGeojson.features
      .map((f) => f.properties.count)
      .filter((c) => c > 0);
    if (counts.length === 0) {
      return [0, "#888", 1, "#888"];
    }

    const min = Math.min(...counts);
    const max = Math.max(...counts);

    if (min === max) {
      return [min, "#fdae61", max, "#fdae61"];
    }

    // Use a blue->yellow->red scale with better distribution
    const mid = min + (max - min) * 0.5;
    return [min, "#2b83ba", mid, "#fdae61", max, "#d7191c"];
  } catch (error) {
    console.error("Error calculating hexbin colors:", error);
    return [0, "#888", 1, "#888"];
  }
}

// Height scale for hexbins based on count with improved scaling
export function getHexbinHeight(hexbinGeojson) {
  if (
    !hexbinGeojson ||
    !hexbinGeojson.features ||
    hexbinGeojson.features.length === 0
  ) {
    return [0, 100, 1, 100];
  }

  try {
    const counts = hexbinGeojson.features
      .map((f) => f.properties.count)
      .filter((c) => c > 0);
    if (counts.length === 0) {
      return [0, 100, 1, 100];
    }

    const min = Math.min(...counts);
    const max = Math.max(...counts);

    if (min === max) {
      return [min, 500, max, 500];
    }

    // Height in meters (extrusion), with better scaling
    return [min, 200, max, 3000];
  } catch (error) {
    console.error("Error calculating hexbin heights:", error);
    return [0, 100, 1, 100];
  }
}

// Build a legend for the hexbin color scale with improved formatting
export function buildLegend(hexbinGeojson, legendId) {
  const legend = document.getElementById(legendId);
  if (!legend) {
    console.warn("Legend element not found:", legendId);
    return;
  }

  try {
    legend.innerHTML = "";
    const stops = getHexbinColor(hexbinGeojson);

    // Create legend items
    for (let i = 0; i < stops.length; i += 2) {
      const count = stops[i];
      const color = stops[i + 1];

      const row = document.createElement("div");
      row.className = "legend-row";

      const colorBox = document.createElement("span");
      colorBox.className = "legend-color";
      colorBox.style.background = color;

      const label = document.createElement("span");
      label.textContent = `≥ ${count} crashes`;

      row.appendChild(colorBox);
      row.appendChild(label);
      legend.appendChild(row);
    }
  } catch (error) {
    console.error("Error building legend:", error);
    legend.innerHTML = '<div class="legend-row">Error loading legend</div>';
  }
}

// Utility function to format large numbers for display
export function formatNumber(num) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
}

// Future: Add time filtering, charts, etc. as new exports
