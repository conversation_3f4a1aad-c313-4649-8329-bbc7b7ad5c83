<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <title>GeoJSON Oluşturucu</title>
    <!-- Turf.js’i global `turf` adıyla kull<PERSON> hazır hale getiriyor -->
    <script src="https://unpkg.com/@turf/turf@6.5.0/turf.min.js"></script>
  </head>
  <body>
    <script>
      // Turf artık burada global turf objesi olarak var
      console.log("Turf version:", turf.version);

      function generateCrashFeaturesInAnkara(count) {
        const bbox = [32.847061, 39.903811, 32.918301, 39.943963];
        const features = [];

        function randomHexId(len = 64) {
          let s = "";
          while (s.length < len) {
            s += Math.random().toString(16).slice(2);
          }
          return s.slice(0, len);
        }

        function randomDate() {
          const now = Date.now();
          const past = now - 30 * 24 * 3600 * 1000;
          const t = new Date(past + Math.random() * (now - past));
          return t.toISOString().replace("Z", ".000");
        }

        const weatherOptions = ["CLEAR", "RAIN", "FOGGY", "SNOW"];
        const lightingOptions = [
          "DAYLIGHT",
          "DUSK",
          "NIGHT, UNLIGHTED ROAD",
          "DARKNESS, LIGHTED ROAD",
        ];
        const controlDevices = [
          "STOP SIGN",
          "TRAFFIC SIGNAL",
          "NONE",
          "ROUNDABOUT",
        ];
        const trafficwayTypes = [
          "FOUR WAY",
          "T-INTERSECTION",
          "DIVIDED",
          "NOT DIVIDED",
          "ROUNDABOUT",
        ];
        const crashTypes = [
          "ANGLE",
          "REAR END",
          "HEAD-ON",
          "SIDESWIPE",
          "PEDESTRIAN",
        ];
        const primaryCauses = [
          "FAILURE TO YIELD",
          "FOLLOWING TOO CLOSELY",
          "SPEEDING",
          "IMPAIRED BY WEATHER",
          "DISTRACTED DRIVING",
        ];
        const speedLimits = ["30", "40", "50", "60", "70"];

        while (features.length < count) {
          const pt = turf.randomPoint(1, { bbox }).features[0];
          // booleanPointInPolygon, turf.bboxPolygon ile doğru çalışır
          if (turf.booleanPointInPolygon(pt, turf.bboxPolygon(bbox))) {
            const [lon, lat] = pt.geometry.coordinates;
            const crashDate = randomDate();
            const d = new Date(crashDate);
            const injuriesTotal = Math.floor(Math.random() * 3);
            const injuriesFatal = Math.random() < 0.05 ? 1 : 0;
            const injuriesIncapacitating = injuriesFatal
              ? 0
              : Math.floor(Math.random() * (injuriesTotal + 1));
            const injuriesNonIncapacitating =
              injuriesTotal - injuriesFatal - injuriesIncapacitating;
            const injuriesNoIndication = Math.max(
              0,
              injuriesTotal -
                injuriesFatal -
                injuriesIncapacitating -
                injuriesNonIncapacitating
            );

            features.push({
              type: "Feature",
              geometry: { type: "Point", coordinates: [lon, lat] },
              properties: {
                crash_record_id: randomHexId(),
                crash_date: crashDate,
                crash_month: String(d.getUTCMonth() + 1),
                crash_day_of_week: String(d.getUTCDay()),
                crash_hour: String(d.getUTCHours()),
                injuries_fatal: String(injuriesFatal),
                injuries_incapacitating: String(injuriesIncapacitating),
                injuries_non_incapacitating: String(injuriesNonIncapacitating),
                injuries_no_indication: String(injuriesNoIndication),
                injuries_unknown: "0",
                injuries_reported_not_evident: "0",
                injuries_total: String(injuriesTotal),
                weather_condition:
                  weatherOptions[
                    Math.floor(Math.random() * weatherOptions.length)
                  ],
                lighting_condition:
                  lightingOptions[
                    Math.floor(Math.random() * lightingOptions.length)
                  ],
                traffic_control_device:
                  controlDevices[
                    Math.floor(Math.random() * controlDevices.length)
                  ],
                trafficway_type:
                  trafficwayTypes[
                    Math.floor(Math.random() * trafficwayTypes.length)
                  ],
                first_crash_type:
                  crashTypes[Math.floor(Math.random() * crashTypes.length)],
                prim_contributory_cause:
                  primaryCauses[
                    Math.floor(Math.random() * primaryCauses.length)
                  ],
                sec_contributory_cause: null,
                posted_speed_limit:
                  speedLimits[Math.floor(Math.random() * speedLimits.length)],
              },
            });
          }
        }

        return { type: "FeatureCollection", features };
      }

      const geojson = generateCrashFeaturesInAnkara(30);
      console.log(JSON.stringify(geojson, null, 2));
      // Dilerseniz buradan AJAX ile kaydedebilir veya <a download> ile indirtirsiniz
    </script>
  </body>
</html>
